import { Linking, Platform } from 'react-native';
import { compatibleFetch, getTecBizNetworkConfig, isAndroid8OrLower } from '../utils/networkConfig';

// Função auxiliar para criar timeout compatível
const createTimeoutSignal = (timeoutMs: number): AbortSignal | undefined => {
  try {
    // Tentar usar AbortSignal.timeout se disponível (versões mais recentes)
    if (typeof AbortSignal !== 'undefined' && typeof (AbortSignal as any).timeout === 'function') {
      return (AbortSignal as any).timeout(timeoutMs);
    }
  } catch (error) {
    console.warn('⚠️ AbortSignal.timeout não disponível, usando fallback');
  }

  // Fallback para Android 8.0 - não usar timeout (será gerenciado pelo compatibleFetch)
  return undefined;
};

// Tipos para Deep Linking
export interface DeepLinkData {
  action: string;
  params: { [key: string]: string };
}

// Função para extrair dados do link de confirmação de email
export const parseEmailConfirmationLink = (url: string): DeepLinkData | null => {
  try {
    console.log('🔗 Analisando link de confirmação:', url);
    
    const urlObj = new URL(url);
    const searchParams = urlObj.searchParams;
    
    // Verificar se é um link de confirmação de email
    const action = searchParams.get('a');
    const modo = searchParams.get('modo');
    const dados = searchParams.get('dados');
    const origem = searchParams.get('origem');
    
    if (action === '657df2' && modo && dados) {
      console.log('✅ Link de confirmação de email válido detectado');

      return {
        action: 'email_confirmation',
        params: {
          modo,
          dados,
          origem: origem || 'WEB', // Aceitar tanto APP quanto WEB
          fullUrl: url
        }
      };
    }
    
    console.log('❌ Link não reconhecido como confirmação de email');
    return null;
    
  } catch (error) {
    console.error('❌ Erro ao analisar link:', error);
    return null;
  }
};

// Função para processar deep links
export const handleDeepLink = (url: string): DeepLinkData | null => {
  try {
    console.log('🔗 Processando deep link:', url);
    console.log('🔗 URL completa recebida:', url);

    // Normalizar URL para evitar problemas de encoding
    let normalizedUrl = url;
    try {
      // Tentar decodificar se estiver encoded
      if (url.includes('%')) {
        normalizedUrl = decodeURIComponent(url);
        console.log('🔗 URL decodificada:', normalizedUrl);
      }
    } catch (decodeError) {
      console.log('⚠️ Erro ao decodificar URL, usando original:', decodeError);
    }

    // Verificar se é link de confirmação de email (HTTP ou HTTPS)
    if (normalizedUrl.includes('www2.tecbiz.com.br') && normalizedUrl.includes('a=657df2')) {
      console.log('🔗 Link de confirmação de email detectado:', normalizedUrl);
      return parseEmailConfirmationLink(normalizedUrl);
    }

    // Verificar se é link customizado do app (tecbizapp://)
    if (normalizedUrl.startsWith('tecbizapp://')) {
      console.log('🔗 Processando esquema tecbizapp://');

      try {
        const urlObj = new URL(normalizedUrl);
        const action = urlObj.hostname || urlObj.pathname.replace('/', '');
        const params: { [key: string]: string } = {};

        // Extrair parâmetros da query string
        urlObj.searchParams.forEach((value, key) => {
          params[key] = value;
        });

        console.log('🔗 Deep link customizado detectado:', {
          action,
          params,
          hostname: urlObj.hostname,
          pathname: urlObj.pathname,
          search: urlObj.search
        });

        // Se é confirmação de email, extrair dados do usuário dos parâmetros
        if (action === 'email_confirmation' && params.codass && params.codent) {
          console.log('✅ Dados do usuário encontrados no deep link:', {
            codass: params.codass,
            codent: params.codent,
            cartao: params.cartao ? params.cartao.substring(0, 4) + '****' : 'N/A',
            emailCadastro: params.emailCadastro,
            portador: params.portador
          });
        }

        return {
          action,
          params
        };
      } catch (urlError) {
        console.error('❌ Erro ao processar URL tecbizapp://', urlError);

        // Fallback: tentar extrair manualmente
        console.log('🔄 Tentando extração manual dos parâmetros...');
        const urlParts = normalizedUrl.replace('tecbizapp://', '').split('?');
        const action = urlParts[0] || 'unknown';
        const params: { [key: string]: string } = {};

        if (urlParts[1]) {
          const queryParams = urlParts[1].split('&');
          queryParams.forEach(param => {
            const [key, value] = param.split('=');
            if (key && value) {
              params[key] = decodeURIComponent(value);
            }
          });
        }

        console.log('🔄 Extração manual resultado:', { action, params });

        return {
          action,
          params
        };
      }
    }

    console.log('❌ URL não reconhecida como deep link válido');
    return null;

  } catch (error) {
    console.error('❌ Erro geral ao processar deep link:', error);
    return null;
  }
};

// Função para configurar listener de deep links
export const setupDeepLinkListener = (
  onDeepLink: (data: DeepLinkData) => void
): (() => void) => {
  console.log('🔗 Configurando listener de deep links');

  // Listener para links recebidos quando o app está aberto
  const handleUrl = (event: { url: string }) => {
    console.log('🔗 Deep link recebido (app aberto):', event.url);
    console.log('🔗 Tipo do evento:', typeof event, event);

    const data = handleDeepLink(event.url);
    if (data) {
      console.log('✅ Deep link processado com sucesso, chamando callback');
      onDeepLink(data);
    } else {
      console.log('❌ Deep link não pôde ser processado');
    }
  };

  // Adicionar listener
  console.log('🔗 Adicionando listener de URL...');
  const subscription = Linking.addEventListener('url', handleUrl);
  console.log('🔗 Listener adicionado:', subscription ? 'Sucesso' : 'Falha');

  // Verificar se o app foi aberto por um deep link
  console.log('🔗 Verificando URL inicial...');
  Linking.getInitialURL().then((url) => {
    console.log('🔗 URL inicial obtida:', url);
    if (url) {
      console.log('🔗 Deep link inicial detectado (app fechado):', url);
      const data = handleDeepLink(url);
      if (data) {
        console.log('✅ Deep link inicial processado, aguardando inicialização do app...');
        // Aguardar mais tempo para o app inicializar completamente (splash screen + navegação)
        // iOS pode precisar de mais tempo
        setTimeout(() => {
          console.log('🚀 Executando callback do deep link inicial');
          onDeepLink(data);
        }, 3000);
      } else {
        console.log('❌ Deep link inicial não pôde ser processado');
      }
    } else {
      console.log('ℹ️ Nenhuma URL inicial encontrada (app aberto normalmente)');
    }
  }).catch((error) => {
    console.error('❌ Erro ao obter URL inicial:', error);
    console.error('❌ Detalhes do erro:', {
      message: error.message,
      name: error.name,
      stack: error.stack?.substring(0, 200)
    });
  });

  // Retornar função para remover listener
  return () => {
    console.log('🔗 Removendo listener de deep links');
    if (subscription?.remove) {
      subscription.remove();
      console.log('✅ Listener removido com sucesso');
    } else {
      console.log('⚠️ Listener não pôde ser removido (subscription inválida)');
    }
  };
};

// Função para confirmar email via API
export const confirmarEmail = async (
  modo: string,
  dados: string
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    console.log('📧 Confirmando email via API:', { modo, dados });

    // NOTA: Os dados do usuário agora vêm diretamente do PHP via deep link
    // Não precisamos mais decodificar ou buscar dados aqui

    // Estratégia de URLs para máxima compatibilidade
    const isAndroid8 = isAndroid8OrLower();
    const isIOS = Platform.OS === 'ios';
    console.log('📱 Detectado Android 8.0 ou inferior:', isAndroid8);
    console.log('🍎 iOS detectado:', isIOS);

    // Para iOS: sempre tentar HTTPS primeiro (mais seguro)
    // Para Android 8.0: usar HTTP primeiro (melhor compatibilidade)
    // Para versões atuais do Android: tentar HTTPS primeiro, HTTP como fallback
    const urlsToTry = isIOS
      ? [
          `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`,
          `http://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`
        ]
      : isAndroid8
      ? [
          `http://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`,
          `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`
        ]
      : [
          `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`,
          `http://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`
        ];

    console.log('🔗 URLs para tentar:', urlsToTry.map(url => url.startsWith('https') ? 'HTTPS' : 'HTTP'));

    let response: Response | null = null;
    let lastError: Error | null = null;

    // Tentar cada URL até uma funcionar
    for (let i = 0; i < urlsToTry.length; i++) {
      const currentUrl = urlsToTry[i];
      const protocol = currentUrl.startsWith('https') ? 'HTTPS' : 'HTTP';

      try {
        console.log(`🔄 Tentativa ${i + 1}/${urlsToTry.length} - ${protocol}:`, currentUrl);

        // Configurar fetch baseado na versão do Android
        const fetchFunction = isAndroid8 ? compatibleFetch : fetch;
        const requestConfig = isAndroid8 ? getTecBizNetworkConfig() : {};

        console.log('📱 Usando fetch compatível:', isAndroid8 ? 'SIM (Android 8.0)' : 'NÃO (Versão atual)');

        // Headers específicos para cada plataforma
        const headers = {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'User-Agent': isIOS ? 'TecBizApp/1.0 (iOS)' : 'TecBizApp/1.0 (Android)',
          'Cache-Control': 'no-cache',
          ...(isAndroid8 ? {} : { 'Accept-Encoding': 'gzip, deflate, br' }),
          ...(isIOS && {
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
          })
        };

        console.log('📋 Headers para', isIOS ? 'iOS' : 'Android', ':', headers);

        response = await fetchFunction(currentUrl, {
          method: 'GET',
          headers,
          ...requestConfig,
          // Timeout específico por tentativa (compatível com Android 8.0)
          signal: isAndroid8 ? undefined : createTimeoutSignal(isIOS ? 20000 : 30000),
          ...(isIOS && {
            // Configurações específicas para iOS
            credentials: 'omit' // Não enviar cookies
          })
        });

        console.log(`✅ Sucesso ${protocol} - Status:`, response.status);
        break; // Sucesso, sair do loop

      } catch (error: any) {
        const errorInfo = {
          message: error.message,
          name: error.name,
          code: error.code,
          stack: error.stack?.substring(0, 200) + '...'
        };

        console.error(`❌ Falha ${protocol}:`, errorInfo);

        // Identificar tipos específicos de erro para Android 8.0
        if (isAndroid8) {
          if (error.message?.includes('SSL') || error.message?.includes('certificate')) {
            console.log('🔒 Erro SSL detectado no Android 8.0 - esperado para HTTPS');
          } else if (error.message?.includes('Network') || error.message?.includes('timeout')) {
            console.log('🌐 Erro de rede detectado no Android 8.0');
          }
        }

        lastError = error;

        // Se não é a última tentativa, continuar
        if (i < urlsToTry.length - 1) {
          console.log(`🔄 Tentando próximo protocolo... (${i + 2}/${urlsToTry.length})`);
          continue;
        }
      }
    }

    // Verificar se conseguiu uma resposta válida
    if (!response) {
      console.error('❌ Todas as tentativas falharam');

      // Criar mensagem de erro específica para diferentes plataformas
      let errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';

      if (lastError) {
        if (lastError.message?.includes('SSL') || lastError.message?.includes('certificate')) {
          errorMessage = 'Erro de certificado SSL. Tente novamente ou verifique sua conexão.';
        } else if (lastError.message?.includes('timeout') || lastError.message?.includes('TIMEOUT')) {
          errorMessage = 'Timeout de conexão. Verifique sua internet e tente novamente.';
        } else if (lastError.message?.includes('Network') || lastError.message?.includes('network')) {
          errorMessage = 'Erro de rede. Verifique sua conexão com a internet.';
        } else if (lastError.message?.includes('Failed to fetch') || lastError.message?.includes('fetch')) {
          errorMessage = 'Falha na conexão com o servidor. Verifique sua internet.';
        }
      }

      const finalError = new Error(errorMessage);
      (finalError as any).originalError = lastError;
      throw finalError;
    }

    console.log('📡 Status final confirmação email:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📄 Resposta confirmação:', responseText.substring(0, 200) + '...');

    // Verificar se a resposta contém indicação de sucesso
    // O servidor agora retorna HTML com redirecionamento para o app
    if (responseText.includes('E-mail validado com sucesso') ||
        responseText.includes('validado com sucesso') ||
        responseText.includes('tecbizapp://email_confirmation') ||
        response.status === 200) {

      console.log('✅ Email confirmado com sucesso');

      // O PHP já processou tudo e enviará os dados via deep link
      // Não precisamos mais processar dados aqui
      console.log('✅ Email confirmado com sucesso - dados virão via deep link');

      return {
        success: true,
        message: 'E-mail confirmado com sucesso!',
        data: {
          confirmed: true,
          timestamp: Date.now()
        }
      };
    } else if (responseText.includes('Link expirado')) {
      console.log('⏰ Link de confirmação expirado');
      return {
        success: false,
        message: 'Link de confirmação expirado. Solicite um novo link.'
      };
    } else {
      console.log('❌ Falha na confirmação do email');
      return {
        success: false,
        message: 'Falha ao confirmar email. Tente novamente.'
      };
    }

  } catch (error) {
    console.error('❌ Erro ao confirmar email:', error);
    return {
      success: false,
      message: 'Erro de conexão. Verifique sua internet e tente novamente.'
    };
  }
};
