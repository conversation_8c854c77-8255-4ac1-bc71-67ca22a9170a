import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, BackHandler, Alert, Dimensions, RefreshControl } from 'react-native';
import { usePushNotifications } from '../store/pushNotificationStore';
// Geolocation removido - incompatível com Expo (usar expo-location se necessário)
import { ApiResponse, MesData, SinteticoData, UsuarioData } from '../services/api';
import Footer from '../components/Footer';
import SafeHeader from '../components/SafeHeader';
import Cores from '../constants/Cores';
import { useAppContext } from '../context/AppContext';
import { useExitConfirmation } from '../hooks/useBackHandler';
import StatusCartao from '../components/StatusCartao';
import SaldoAtualizado from '../components/SaldoAtualizado';
import ConnectionError from '../components/ConnectionError';
import { useUserStore } from '../store/useUserStore';
import { getReloadCredentials, clearLoginData } from '../utils/persistentStorage';
import { robustStorage } from '../utils/robustStorage';
import NotificationIcon from '../components/NotificationIcon';
import { NavigationProp } from '../types/navigation';
import ResponsiveScrollView from '../components/ResponsiveScrollView';
import ResponsiveText from '../components/ResponsiveText';
import {
  getResponsivePadding,
  getResponsiveSpacing,
  getResponsiveTextSize,
  isSmallScreen,
  isMediumSmallScreen,
  isProblematicResolution,
  getProblematicResolutionAdjustments
} from '../utils/responsive';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getSafeBottomPadding, useSafeInsets } from '../utils/safeArea';


interface HomeScreenProps {
  navigation: NavigationProp;
  route: {
    params: { name?: string; userData?: ApiResponse } | null;
  };
}

export default function HomeScreen({ navigation, route }: HomeScreenProps) {
  const userData = route.params?.userData;
  //const [currentLocation, setCurrentLocation] = useState<string>('Carregando...');
  const { autorizacoesAtualizadas, setAutorizacoesAtualizadas, setUserData: setUserDataContext } = useAppContext();
  const setUserData = useUserStore(state => state.setUserData);
  const [refreshing, setRefreshing] = useState(false);
  const [showConnectionError, setShowConnectionError] = useState(false);

  // Dimensões da tela para responsividade
  const SCREEN_WIDTH = Dimensions.get('window').width;

  // Responsividade melhorada
  const isSmall = isSmallScreen();
  const isMediumSmall = isMediumSmallScreen();
  const isProblematic = isProblematicResolution();
  const adjustments = getProblematicResolutionAdjustments();

  // Safe area insets para ajustar padding do footer
  const insets = useSafeInsets();
  const safeBottomPadding = getSafeBottomPadding(30); // 30 é o padding base

  // Push notifications para debug
  const { fcmToken } = usePushNotifications();

  // Controle de botão voltar com confirmação de saída
  useExitConfirmation('Home');

  // Função de recarregamento automático (igual ao Cordova)
  const recarregar = async () => {
    try {
      console.log('🔄 Recarregando dados automaticamente...');

      // Obter dados salvos do robustStorage (mesmo usado no login)
      const cartaoSalvo = await robustStorage.getItem('cartao_login');
      const senhaSalva = await robustStorage.getItem('senha_login');

      console.log('🔍 Verificando dados salvos:');
      console.log('   - Cartão:', cartaoSalvo ? cartaoSalvo.substring(0, 10) + '...' : 'não encontrado');
      console.log('   - Senha:', senhaSalva ? '***' : 'não encontrada');

      if (!cartaoSalvo || !senhaSalva) {
        console.log('ℹ️ Cartão/senha não salvos - recarregamento automático não disponível');

        // Tentar fallback com persistentStorage
        const { cartao: cartaoFallback, senha: senhaFallback } = await getReloadCredentials();
        if (cartaoFallback && senhaFallback) {
          console.log('🔄 Usando dados do fallback storage');
          return await fazerRequisicaoRecarregamento(cartaoFallback, senhaFallback);
        }

        return false;
      }

      console.log('📡 Fazendo requisição com dados salvos (cartão:', cartaoSalvo.substring(0, 10) + '...');
      return await fazerRequisicaoRecarregamento(cartaoSalvo, senhaSalva);
    } catch (error) {
      console.log('❌ Erro no recarregamento automático:', error);
      setShowConnectionError(true);
      return false;
    }
  };

  // Função auxiliar para fazer a requisição
  const fazerRequisicaoRecarregamento = async (cartao: string, senha: string) => {
    try {

      // Fazer requisição igual ao Cordova (usando login API)
      const { login } = require('../services/api');
      const response = await login(cartao, senha);

      if (response.success && response.data) {
        console.log('✅ Dados recarregados com sucesso');

        // Atualizar contexto com novos dados (igual localStorage.set no Cordova)
        setUserDataContext(response.data);

        // Atualizar Zustand store também
        setUserData(response.data);

        return true;
      } else {
        console.log('❌ Falha no recarregamento:', response.message);

        // Verificar se é erro de senha inválida (credenciais alteradas)
        if (response.message?.toLowerCase().includes('senha') ||
            response.message?.toLowerCase().includes('invalid') ||
            response.message?.toLowerCase().includes('unauthorized') ||
            response.message?.toLowerCase().includes('credencial')) {

          console.log('🔐 Credenciais inválidas detectadas - fazendo logout automático');

          // Mostrar alerta e fazer logout
          Alert.alert(
            'Sessão Expirada',
            'Suas credenciais foram alteradas em outro dispositivo. Você será redirecionado para o login.',
            [
              {
                text: 'OK',
                onPress: async () => {
                  // Limpar dados salvos
                  await clearLoginData();
                  // Redirecionar para login
                  navigation.navigate('Login');
                }
              }
            ],
            { cancelable: false }
          );

          return false;
        }

        // Verificar se é timeout e mostrar modal de erro
        if (response.message === 'TIMEOUT' || (response as any).isTimeout) {
          console.log('⏰ Timeout detectado - mostrando modal de erro');
          setShowConnectionError(true);
        }

        return false;
      }
    } catch (error) {
      console.log('❌ Erro no recarregamento automático:', error);

      // Mostrar modal de erro para qualquer erro de conexão
      setShowConnectionError(true);

      return false;
    }
  };

  // Tornar função recarregar acessível globalmente para outras telas
  useEffect(() => {
    (globalThis as any).recarregarHomeScreen = recarregar;

    console.log('🏠 HomeScreen carregada - iniciando recarregamento automático');

    // Pequeno delay para não conflitar com outras operações
    const timer = setTimeout(() => {
      recarregar();
    }, 500);

    return () => {
      clearTimeout(timer);
      // Limpar referência global
      delete (globalThis as any).recarregarHomeScreen;
    };
  }, []); // Executa APENAS uma vez quando a tela é montada

  // Função para atualizar dados via pull-to-refresh (usa mesma lógica do recarregar)
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      console.log('🔄 Pull-to-refresh acionado');
      const sucesso = await recarregar();
      if (!sucesso) {
        console.log('⚠️ Recarregamento via pull-to-refresh falhou');
      }
    } catch (error) {
      console.log('❌ Erro no pull-to-refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Controlar o botão de voltar
  useEffect(() => {
    const backAction = () => {
      Alert.alert('Sair', 'Deseja voltar para a tela de login?', [
        {
          text: 'Cancelar',
          onPress: () => null,
          style: 'cancel',
        },
        {
          text: 'Sim', onPress: async () => {
            // Limpar dados de login salvos do storage persistente
            await clearLoginData();
            console.log('🧹 Dados de login limpos no logout');
            navigation.navigate('Login');
          }
        },
      ]);
      return true;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [navigation]);

  // Obter localização atual
  /*
  useEffect(() => {
    getCurrentLocation();
  }, []);
*/
  // Reagir às mudanças de autorizações
  // Inicializar Zustand com dados do login
  useEffect(() => {
    if (userData) {
      setUserData(userData);
    }
  }, [userData]); // Removido setUserData das dependências

  useEffect(() => {
    if (autorizacoesAtualizadas) {
      console.log('🔄 Autorizações foram atualizadas');
      setAutorizacoesAtualizadas(false); // Reset do flag
    }
  }, [autorizacoesAtualizadas, setAutorizacoesAtualizadas]);

  /*
  // Função para solicitar permissão de localização
  const requestLocationPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Permissão de Localização',
            message: 'Este app precisa acessar sua localização para mostrar a cidade atual.',
            buttonNeutral: 'Perguntar depois',
            buttonNegative: 'Cancelar',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Erro ao solicitar permissão:', err);
        return false;
      }
    }
    return true;
  };


  // Função para obter localização atual
  const getCurrentLocation = async () => {
    try {
      const hasPermission = await requestLocationPermission();

      if (!hasPermission) {
        setCurrentLocation('Permissão de localização negada');
        return;
      }

      setCurrentLocation('Obtendo localização...');

      // Simulação de coordenadas (Porto Alegre, RS como exemplo)
      // Em produção, aqui seria usado Geolocation.getCurrentPosition
      setTimeout(() => {
        const latitude = -30.0346; // Porto Alegre, RS
        const longitude = -51.2177;
        console.log('Coordenadas simuladas:', { latitude, longitude });
        reverseGeocode(latitude, longitude);
      }, 2000);

    } catch (error) {
      console.log('Erro geral na geolocalização:', error);
      setCurrentLocation('Erro na geolocalização');
    }
  };

  // Função para converter coordenadas em nome da cidade
  const reverseGeocode = async (latitude: number, longitude: number) => {
    try {
      console.log('Fazendo reverse geocoding para:', { latitude, longitude });

      // Usando a API do OpenStreetMap Nominatim (gratuita)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'TecBizApp/1.0'
          }
        }
      );

      const data = await response.json();
      console.log('Dados do geocoding:', data);

      if (data && data.address) {
        //const city = data.address.city || data.address.town || data.address.village || data.address.county || 'Cidade não encontrada';
        const city = data.address.municipality || 'Cidade não encontrada';
        const state = data.address.state || '';
        const location = `${city}${state ? `, ${state}` : ''}`;
        console.log('Localização encontrada:', location);
        setCurrentLocation(location);
      } else {
        setCurrentLocation('Cidade não encontrada');
      }
    } catch (error) {
      console.log('Erro ao obter nome da cidade:', error);
      setCurrentLocation('Erro ao obter cidade');
    }
  }; */

  // Se não há dados, redireciona para login
  if (!userData || !Array.isArray(userData)) {
    return (
      <View style={styles.container}>
        <SafeHeader
          title="TecBiz Associado"
          showBackButton={false}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Dados não encontrados</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.buttonText}>Voltar ao Login</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Extrair dados do usuário da resposta da API
  const usuarioItem = userData.find(item => 'usuario' in item) as UsuarioData | undefined;
  const sinteticoItem = userData.find(item => 'sintetico' in item) as SinteticoData | undefined;

  // Se não encontrou os dados necessários
  if (!usuarioItem || !sinteticoItem) {
    return (
      <View style={styles.container}>
        <SafeHeader
          title="TecBiz Associado"
          showBackButton={false}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Dados incompletos recebidos da API</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.buttonText}>Tentar Novamente</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Dados do usuário extraídos da API
  const userInfo = {
    name: usuarioItem.usuario.portador,
    situacao: usuarioItem.usuario.situacao,
    mesAtual: usuarioItem.usuario.mes_atual,
    saldoMensal: sinteticoItem.sintetico.saldo_mensal,
    limiteMensal: sinteticoItem.sintetico.limite_mensal,
    limiteTotal: sinteticoItem.sintetico.limite_total,
    // Só inclui saldoTotal se existir no JSON
    saldoTotal: sinteticoItem.sintetico.saldo_total || null,
    cartaoFormatado: usuarioItem.usuario.cartaoFormatado,
    entidade: usuarioItem.usuario.etd,
  };

  // Verificar se saldo_total existe no JSON original
  const temSaldoTotal = sinteticoItem.sintetico.hasOwnProperty('saldo_total') &&
    sinteticoItem.sintetico.saldo_total !== null &&
    sinteticoItem.sintetico.saldo_total !== undefined &&
    sinteticoItem.sintetico.saldo_total !== '';

  console.log('🔍 Verificação saldo_total:', {
    existe: sinteticoItem.sintetico.hasOwnProperty('saldo_total'),
    valor: sinteticoItem.sintetico.saldo_total,
    temSaldoTotal
  });

  // Função para obter status e escrever por extenso
  const getStatusText = (situacao: string) => {
    switch (situacao) {
      case 'L': return 'Liberado';
      case 'B': return 'Bloqueado';
      case 'C': return 'Cancelado';
      default: return 'Desconhecido';
    }
  };

  // Função para obter cor do status
  const getStatusColor = (situacao: string) => {
    switch (situacao) {
      case 'L': return 'green';
      case 'B': return 'red';
      default: return 'gray';
    }
  };

  return (
    <View style={styles.container}>
      <SafeHeader
        title="TecBiz Associado"
        showBackButton={false}
        rightComponent={
          <View style={styles.headerRight}>
            <NotificationIcon
              size={24}
              color="#fff"
              onPress={() => navigation.navigate('Notifications')}
            />
            <TouchableOpacity
              style={styles.logoutButton}
              onPress={() => {
                Alert.alert(
                  'Sair',
                  'Deseja realmente sair do aplicativo?',
                  [
                    { text: 'Cancelar', style: 'cancel' },
                    {
                      text: 'Sair', onPress: async () => {
                        // Limpar dados de login salvos do storage persistente
                        await clearLoginData();
                        console.log('🧹 Dados de login limpos no logout');
                        navigation.navigate('Login');
                      }
                    }
                  ]
                );
              }}
            >
              <Text style={styles.logoutIcon}>→</Text>
              <Text style={styles.logoutText} numberOfLines={1}>Sair</Text>
            </TouchableOpacity>
          </View>
        }
      />

      <ResponsiveScrollView
        style={styles.scrollView}
        containerStyle={{ ...styles.scrollContainer, paddingBottom: safeBottomPadding }}
        showsVerticalScrollIndicator={false}
        enableAutoHorizontalScroll={true}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Cores.primaria]}
            tintColor={Cores.primaria}
          />
        }
      >
        {/* Informações do usuário */}
        <View style={styles.userSection}>
          <Text
            style={[
              styles.welcomeText,
              {
                fontSize: getResponsiveTextSize(isSmall ? 16 : isMediumSmall ? 18 : 20) + (adjustments.fontSize || 0)
              }
            ]}
            numberOfLines={1}
            adjustsFontSizeToFit
            minimumFontScale={0.8}
          >
            Olá, {userInfo.name}!
          </Text>

          {/* TODO: Remover localização temporariamente
          <Text style={styles.userLocation}>📍 {currentLocation}</Text> */}
        </View>

        {/* Saldo Atualizado Automaticamente */}
        <SaldoAtualizado
          onPress={() => navigation.navigate('Saldo')}
          style={styles.saldoContainer}
        />

        {/* Informações adicionais */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>Mês atual: {userInfo.mesAtual}</Text>
          {userInfo.cartaoFormatado && (
            <Text style={styles.infoText}>Cartão: {userInfo.cartaoFormatado}</Text>
          )}
          <View style={styles.statusCartaoContainer}>
            <Text style={styles.infoText}>Status: </Text>
            <StatusCartao fontSize={16} />
          </View>
          {userInfo.entidade && (
            <Text style={styles.infoText}>{userInfo.entidade}</Text>
          )}
        </View>

        {/* Menu de opções */}
        <View style={styles.menuContainer}>
          {/* Primeiro bloco */}
          <View style={styles.menuRow}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate('RedeConveniada')}
            >
              <Text style={styles.menuIcon}>🏪</Text>
              <Text
                style={[
                  styles.menuText,
                  adjustments.flexAdjustments,
                  { fontSize: getResponsiveTextSize(isSmall ? 10 : isMediumSmall ? 11 : 12) + (adjustments.fontSize || 0) }
                ]}
                numberOfLines={2}
                adjustsFontSizeToFit
                minimumFontScale={0.6}
              >
                {isSmall ? 'Rede' : 'Rede\nConveniada'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate('Extrato')}
            >
              <Text style={styles.menuIcon}>📄</Text>
              <Text style={styles.menuText}>Extrato</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate('Saldo')}
            >
              <Text style={styles.menuIcon}>💰</Text>
              <Text style={styles.menuText}>Saldo</Text>
            </TouchableOpacity>
          </View>

          {/* Segundo bloco */}
          <View style={styles.menuRow}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate('CartaoVirtual')}
            >
              <Text style={styles.menuIcon}>💳</Text>
              <Text style={styles.menuText}>Cartão{'\n'}Virtual</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate('BloquearCartao')}
            >
              <Text style={styles.menuIcon}>🔒</Text>
              <Text style={styles.menuText}>Bloquear{'\n'}Cartão</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate('AlterarSenha')}
            >
              <Text style={styles.menuIcon}>🔑</Text>
              <Text style={styles.menuText}>Alterar Senha</Text>
            </TouchableOpacity>
          </View>
        </View>


      </ResponsiveScrollView>

      {/* Footer */}
      <Footer currentScreen="Home" navigation={navigation} />

      {/* Modal de erro de conexão */}
      <ConnectionError
        visible={showConnectionError}
        onRetry={() => {
          setShowConnectionError(false);
          recarregar();
        }}
        onCancel={() => {
          setShowConnectionError(false);
        }}
        message="Problema ao conectar ao servidor. A conexão demorou mais de 1 minuto. Verifique sua internet e tente novamente."
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: 10,
    paddingBottom: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginLeft: 15,
    padding: 8,
  },
  iconText: {
    fontSize: 20,
    color: Cores.textoBranco,
  },
  logoutButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 20, // Aumentado de 15 para 20
    marginRight: 10, // Adicionado respiro da borda direita
    paddingHorizontal: 12, // Aumentado de 8 para 12
    paddingVertical: 6, // Aumentado de 5 para 6
    minWidth: 45, // Aumentado de 40 para 45
  },
  logoutIcon: {
    fontSize: 18,
    color: Cores.textoBranco,
    fontWeight: 'bold',
    transform: [{ rotate: '45deg' }],
  },
  logoutText: {
    fontSize: 10,
    color: Cores.textoBranco,
    marginTop: 2,
    fontWeight: '600',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContainer: {
    paddingBottom: 30,
  },
  userSection: {
    backgroundColor: Cores.fundoPrincipal,
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 8,
    textAlign: 'center',
  },
  userLocation: {
    fontSize: 14,
    color: Cores.textoMedio,
    textAlign: 'center',
    marginTop: 5,
  },
  saldosContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 15,
  },
  saldosContainerSingle: {
    paddingHorizontal: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  saldoCard: {
    flex: 1,
    backgroundColor: Cores.fundoCard,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  saldoCardSingle: {
    backgroundColor: Cores.fundoCard,
    padding: 25,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    minWidth: 280,
    maxWidth: '85%',
  },
  saldoValor: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Cores.primaria,
    marginVertical: 8,
  },
  saldoDescricao: {
    fontSize: 12,
    color: Cores.textoMedio,
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: Cores.fundoCard,
    marginHorizontal: 20,
    marginBottom: 15,
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoText: {
    fontSize: 14,
    color: Cores.textoMedio,
    marginBottom: 5,
  },
  statusCartaoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  saldoContainer: {
    marginBottom: 20,
  },
  userStatus: {
    fontSize: 16,
    color: Cores.sucesso,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  userInfo: {
    fontSize: 14,
    color: Cores.textoMedio,
  },
  menuContainer: {
    paddingHorizontal: 25,
    paddingTop: 10,
  },
  menuRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getResponsiveSpacing(20),
  },
  menuItem: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 15,
    padding: isSmallScreen() ? 6 : 12,
    alignItems: 'center',
    justifyContent: 'center',
    width: isSmallScreen() ? '32%' : '30%', // Mais espaço em telas pequenas
    minHeight: isSmallScreen() ? 70 : 85,
    maxHeight: isSmallScreen() ? 85 : 100, // Limitar altura máxima
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  menuItemFull: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  menuIcon: {
    fontSize: isSmallScreen() ? 20 : 24, // Diminuído de 24/28 para 20/24
    marginBottom: isSmallScreen() ? 4 : 6, // Diminuído margem também
  },
  menuText: {
    fontSize: isSmallScreen() ? 10 : getResponsiveTextSize(12),
    color: Cores.primaria,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: isSmallScreen() ? 12 : getResponsiveTextSize(14),
    flexShrink: 1, // Permitir que o texto encolha se necessário
  },
  menuItemPlaceholder: {
    flex: 1,
    marginHorizontal: 10,
  },

  saldoLabel: {
    fontSize: 12,
    color: Cores.textoMedio,
    textAlign: 'center',
    marginBottom: 2,
  },
  saldoValue: {
    fontSize: 16,
    color: Cores.primaria,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: Cores.textoEscuro,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    backgroundColor: Cores.primaria,
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  buttonText: {
    color: Cores.textoBranco,
    fontSize: 16,
    fontWeight: 'bold',
  },
  locationContainer: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    padding: 15,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  locationLabel: {
    fontSize: 12,
    color: Cores.textoMedio,
    marginBottom: 2,
  },
  locationValue: {
    fontSize: 14,
    color: Cores.primaria,
    fontWeight: 'bold',
  },

});