<?php

include_once "./bd/bd.php";
include_once "./bd/sql.php";
include_once "./bd/sqlRestricao.php";
include_once "./interface/web/paginaPadrao.php";
include_once "./logica/controle/senha.php";
include_once "./logica/extras/string.php";
include_once "./logica/extras/mensagem_erro.php";
include_once "./logica/elementos/entidade.php";
include_once "./logica/elementos/fonte_pg.php";
include_once "./logica/elementos/associado.php";
include_once "./logica/elementos/saldo.php";
include_once "./logica/elementos/cartao.php";
include_once "./logica/elementos/mes.php";
include_once "./logica/elementos/restricoes/restricao.php";
include_once "./logica/elementos/restricoes/restricaoEntidade.php";
include_once "./logica/elementos/EnviaEmailLinkConfirmaEmail.php";
include_once "./logica/elementos/proximo_corte.php";
include_once "./interface/web/HTMLger/gera_popup.php";

class PGAssLogin extends PaginaPadrao
{
	public $erro;
	public $bd;
	public $sql;
	public $subtemplate;
	public $entidade;
	public $fonte_pg;
	public $associado;
	public $cartao;
	public $senhaAdm = false;
	public $nome_meses = array("", "Janeiro", "Fevereiro", "Mar�o", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro");

	function __construct($VARS)
	{
		$this->inicializa_pagina();
		$this->erro = new Erro();
		$this->bd = new BD();
		$this->sql = new SQL();
		$this->subtemplate = "ass_login";
		$this->titulo = "P�gina inicial";

		$_REQUEST['email'] = isset($_REQUEST['email']) ? $_REQUEST['email'] : null;

		if (trim($_REQUEST['email'])) {
			$this->valida_email($VARS);
		} else {
			$this->valida_cartao($VARS);
		}

		if (!$this->erro->ha_erros()) {
			$this->valida_cartao_tef();

			$this->verifica_senha($this->cartao->associado->entidade->codigo, $this->cartao->associado->codigo, $VARS["senha"]);

			if (!$this->erro->ha_erros()) {
				$this->sessao->set_tipo_usuario("associado");
				$this->consulta_dados_associado($VARS);
				if (!isset($this->senhaAdm)) {
					header("location: ./tecbiz.php?a=f14fc6");
				}
			}
		}

		$this->exibe_mensagem();

		if (!$this->senhaAdm) {
			$this->verificaNotificacoesAut();
		}

		$this->monta_pg();
		$this->imprime_pagina();
	}

	function valida_cartao_tef()
	{
		if (strlen($this->cartao->associado->ultimoCartao) <> 16) {
			header("location: ./tecbiz.php?a=5b1a27");
		}
	}

	function valida_email($VARS)
	{
		$email = $_REQUEST['email'];
		$this->bd->consulta($this->sql->dados_associado_por_email($email));
		$r = $this->bd->proximo_registro();

		$qtdEmails = $this->bd->num_linhas();

		if ($qtdEmails == 1) {
			$ass = new Associado($r['asscodass'], new Entidade($r['entcodent']));
			$cartao = $ass->ultimoCartao;
			$this->cartao = new Cartao($cartao);
		} else {
			print "<script>alert('**ATEN��O**\\n\\nEndere�o de e-mail n�o cadastrado. [" . $qtdEmails . "]')</script>";
			echo "<meta http-equiv=\"refresh\" content=\"0;url=./tecbiz.php?a=842228&endereco=$email\">";
			die();
		}
	}

	function valida_cartao($VARS)
	{
		$cartao1 = $_REQUEST['cartao1'];
		$cartao2 = $_REQUEST['cartao2'];
		$cartao3 = $_REQUEST['cartao3'];
		$cartao4 = $_REQUEST['cartao4'];
		$cartao = $cartao1 . $cartao2 . $cartao3 . $cartao4;

		if (!trim($cartao)) {
			$cartao = $_SESSION['numcar'];
		}

		$this->cartao = new Cartao($cartao);

		if (!$this->cartao->existe()) {
			print "<script>alert('**ATEN��O**\\n\\nNumero de cart�o inv�lido.')</script>";
			echo "<meta http-equiv=\"refresh\" content=\"0;url=./tecbiz.php?a=842228&endereco=" . $_REQUEST['email'] . "&cartao1=" . $_REQUEST['cartao1'] . "&cartao2=" . $_REQUEST['cartao2'] . "&cartao3=" . $_REQUEST['cartao3'] . "&cartao4=" . $_REQUEST['cartao4'] . "\">";
			die();
		}
	}

	function verifica_senha($codent, $codass, $senha)
	{
		$senha = new Senha($senha, $this->bd, $this->sql);

		if ($senha->eh_senha_adm()) {
			$this->senhaAdm = true;
			$this->sessao->set('tbz_senha_padrao', false);
			return;
		} else {
			if ($senha->ass_tem_senha($codent, $codass)) {
				if ($senha->eh_senha_ass($codent, $codass)) {
					$this->sessao->set('tbz_senha_padrao', false);
					if ($senha->ass_tem_senha_provisoria($codent, $codass) === 't') {
						$this->senhaAdm = true;
						$this->subtemplate = "ass_alt_senha_prim";
						$this->titulo = "Bem-vindo ao Sistema TecBiz!!";
						$this->sessao->set('tbz_senha_padrao', true);
						return;
					}

					if (!$this->emaiEstalValidado($codent, $codass) && trim($this->Associado->asslogema)) {
						$EnviaEmailLinkConfirmaEmail = new EnviaEmailLinkConfirmaEmail($this->Associado);
						$EnviaEmailLinkConfirmaEmail->enviaEmail();

						print "<script>alert('**ATEN��O**\\n\\n" . str_replace(PHP_EOL, '\n\n', mensagem_erro(125)) . "\\n\\nSer� enviado um e-mail com instru��es de valida��o!')</script>";
						echo "<meta http-equiv=\"refresh\" content=\"0;url=./tecbiz.php?a=842228&endereco=" . $_REQUEST['email'] . "&cartao1=" . $_REQUEST['cartao1'] . "&cartao2=" . $_REQUEST['cartao2'] . "&cartao3=" . $_REQUEST['cartao3'] . "&cartao4=" . $_REQUEST['cartao4'] . "\">";
						die();
					}
					return;
				} else {
					print "<script>alert('**ATEN��O**\\n\\nSenha inv�lida.')</script>";
					echo "<meta http-equiv=\"refresh\" content=\"0;url=./tecbiz.php?a=842228&endereco=" . $_REQUEST['email'] . "&cartao1=" . $_REQUEST['cartao1'] . "&cartao2=" . $_REQUEST['cartao2'] . "&cartao3=" . $_REQUEST['cartao3'] . "&cartao4=" . $_REQUEST['cartao4'] . "\">";
					die();
				}
			} else {

				if ($senha->eh_senha_padrao()) {
					$this->senhaAdm = true;
					$this->subtemplate = "ass_alt_senha_prim";
					$this->titulo = "Bem-vindo ao Sistema TecBiz!!";
					$this->sessao->set('tbz_senha_padrao', true);
				} else {
					print "<script>alert('**ATEN��O**\\n\\nSenha inv�lida.')</script>";
					echo "<meta http-equiv=\"refresh\" content=\"0;url=./tecbiz.php?a=842228&endereco=" . $_REQUEST['email'] . "&cartao1=" . $_REQUEST['cartao1'] . "&cartao2=" . $_REQUEST['cartao2'] . "&cartao3=" . $_REQUEST['cartao3'] . "&cartao4=" . $_REQUEST['cartao4'] . "\">";
					die();
				}
			}
		}
	}

	private function emaiEstalValidado($codent, $codass)
	{
		$this->Associado = new Associado($codass, new Entidade($codent));
		if (!$this->Associado->emailValidado) {
			return false;
		}
		return true;
	}

	function consulta_dados_associado($VARS)
	{
		$restricao = new RestricaoEntidade($this->cartao->associado->entidade, $this->cartao->associado->codfpg);

		if ($restricao->ass_acessa === 'f') {
			$this->sessao->set('tipouser', null);
			$this->erro->registra('<b>Aten��o<br /><br />Acesso bloqueado!!</b><br />Entre em contato com sua **TBZ_NOME_PADRAO_ETD**');
			$this->esconderMenu = true;
		}

		if ($restricao->usaLimiteTotal === "t") {
			$saldo = new Saldo($this->cartao->associado);
			$limiteTotal = number_format($this->cartao->associado->limiteTotal, 2, ",", " ");
			$saldoTotal = number_format($saldo->limite_total, 2, ",", " ");
			$limite_total = "<TR class='td2'>
								<TD CLASS='menu' >Limite total:</TD>	
								<TD CLASS='menu' ALIGN='RIGHT' >R$ $limiteTotal</TD>	
							</TR>
							<TR class='td3'>
								<TD CLASS='menu'>Saldo total:</TD>	
								<TD CLASS='menu' ALIGN='RIGHT'>R$ $saldoTotal</FONT></TD>	
							</TR>";
			$this->template->associa("**USA_LIMITE_TOTAL**", $limite_total);
		} else {
			$this->template->associa("**USA_LIMITE_TOTAL**", "");
		}

		$this->bd->consulta($this->sql->dados_associado($this->cartao->associado->entidade->codigo, $this->cartao->associado->codigo));
		$registro = $this->bd->proximo_registro();
		$this->sessao->set("CodAss", $registro["asscodass"]);
		$this->sessao->set("NomAss", $registro["assnomass"]);
		$this->sessao->set("LimCre", $registro["asslimcre"]);
		$this->sessao->set("NumCar", $registro["carnumcar"]);
		$this->sessao->set("CodEnt", $registro["entcodent"]);
		$this->sessao->set("NomEnt", $registro["entrazsoc"]);
		$this->sessao->set("ID", "On-Line: " . $registro["assnomass"] . " (" . $registro["entcodent"] . "/" . $registro["asscodass"] . ")");
		$this->sessao->set("EndEnt", $registro["entlogend"] . ", " . $registro["entendnum"] . ", " . $registro["ententbai"] . ", " . $registro["cidnomcid"] . ", " . $registro["cidcodest"]);
		$this->sessao->set("FonEnt", $registro["entendfon"]);
		$this->sessao->set("CodFpg", $this->cartao->associado->fontepg->codigo);
		$this->calcula_mes_atual($registro["entdiacor"]);
		$this->associa_valores_sobre_associado($registro);
		$this->associa_valores_sobre_entidade($registro);
		$this->associa_valores_sobre_situacao($registro);
		$this->verifica_cartoes_liberados();
		$this->verifica_periodo_bloqueio_entidade();
		$_SESSION['associado'] 	= $registro["assnomass"];
		$_SESSION['entidade'] 	= $registro["entcodent"];
		$_SESSION['validacao']	= '1';
		$array 	= gera_corte($this->sessao->get('CodEnt'), $this->sessao->get("CodAss"));
		$this->template->associa("**PERIODOCOMPRA**", $array[$this->sessao->get("CodAss")]);
		$data = DateTime::createFromFormat('d/m/Y', $array[$this->sessao->get("CodAss")]);
		$this->template->associa("**MELHORDIACOMPRA**", $data->modify('+1 day')->format('d/m/Y'));
	}

	function associa_valores_sobre_associado($associado)
	{
		$this->template->associa("**NOMASS**", $associado["assnomass"]);
		$this->template->associa("**MATASS**", $associado["assmatass"]);
		$this->template->associa("**CODASS**", $associado["asscodass"]);
		$this->template->associa("**NUMCAR**", formata_cartao_tela($associado["carnumcar"]));

		if ($associado["asslogema"]) {
			$link = "<A HREF=\"./tecbiz.php?a=f14fc6&id=2\" ><FONT SIZE=\"2\">";
			$this->template->associa("**CADASTRO**", $link . 'Alterar</a>');
			$this->template->associa("**EMAIL**", $associado["asslogema"]);
			$this->template->associa("**IDCOD**", '2');
		} else {
			$link = "<A HREF=\"./tecbiz.php?a=f14fc6&id=1\" ><FONT SIZE=\"2\">";
			$this->template->associa("**CADASTRO**", $link . 'Cadastrar</a>');
			$this->template->associa("**EMAIL**", "<font color=\"Red\">Sem e-mail cadastrado</font>");
			$this->template->associa("**IDCOD**", '1');
		}
		$this->template->associa('**POPUP**', '');
	}

	function associa_valores_sobre_entidade($entidade)
	{
		$this->template->associa("**NOMENT**", $entidade["entrazsoc"]);
		$this->template->associa("**ENDERECO**", $entidade["entlogend"]);
		$this->template->associa("**ENDNUMERO**", $entidade["entendnum"]);
		$this->template->associa("**ENDCOMPLEMENTO**", $entidade["entendcmp"]);
		$this->template->associa("**ENDBAIRRO**", $entidade["ententbai"]);
		$this->template->associa("**CIDADE**", $entidade["cidnomcid"]);
		$this->template->associa("**ESTADO**", $entidade["cidcodest"]);
		$this->template->associa("**ENTFONE**", $entidade["entendfon"]);
		$this->template->associa("**A**", '<!--');
	}

	function associa_valores_sobre_situacao($entidade)
	{
		$this->template->associa("**DIACORTE**", $entidade["entdiacor"]);
		$this->template->associa(
			"**LIMCRE**",
			number_format($entidade["asslimcre"], 2, ",", " ")
		);
		$this->template->associa("**MESATUAL**", $this->nome_meses[$this->sessao->get("MesAtu")]);
		$saldo = $this->retorna_saldo();
		$this->bd->consulta($this->sql->total_associado_no_mes($this->sessao->get("CodEnt"), $this->sessao->get("CodAss"), $this->fonte_pg->mes_universal_atual));
		$registro = $this->bd->proximo_registro();
		$this->template->associa("**TOTAL**", number_format($registro["total"], 2, ",", "."));
		$this->template->associa("**SALDO**", number_format($saldo, 2, ",", "."));
	}

	function verifica_cartoes_liberados()
	{
		$this->bd->consulta($this->sql->lista_cartao_titular_liberado($this->sessao->get("CodEnt"), $this->sessao->get("CodAss")));
		$arrayStatus = array('C' => 'CANCELADO', 'B' => 'BLOQUEADO');
		$mensagem = null;
		if (!$this->bd->proximo_registro()) {
			$mensagem = 'ATEN��O: CART�O/CADASTRO ' . $arrayStatus[$this->cartao->status];
		}

		if ($this->cartao->status == 'L') {
			$this->template->associa("**OBS2**", '');
		} else {
			$this->template->associa("**OBS2**", $this->cartao->observacao);
		}

		$associado = new Associado($this->sessao->get("CodAss"), new Entidade($this->sessao->get("CodEnt")));

		if ($associado->situacao !== "0" && trim($associado->situacao)) {
			if (strlen($mensagem) == 0) {
				$mensagem = "<FONT COLOR=\"#A00000\" SIZE=\"3\"><B><U>ATEN��O: ASSOCIADO BLOQUEADO</U></B></FONT><BR>" . "<FONT SIZE=\"2\" COLOR=\"BLACK\">";
				$mensagem .= "<BR>Entre em contato com sua Entidade.";
			}
		} else {
			if ($this->cartao->associado->getSitAssPeriodo() == 'B') {
				$mensagem = "<FONT COLOR=\"#A00000\" SIZE=\"3\"><B><U>ATEN��O: ASSOCIADO BLOQUEADO POR PER�ODO</U></B></FONT><BR>" . "<FONT SIZE=\"2\" COLOR=\"BLACK\">";
				$mensagem .= "<BR>Entre em contato com sua Entidade.";
			}
		}

		if (isset($mensagem)) {
			$this->template->associa("**OBS**", $mensagem);
		}
	}

	function monta_pg()
	{
		if ($this->erro->ha_erros()) {
			$this->titulo = "Erro de Login";
			$this->configura_subtemplate("erro");
			$this->template->associa("**MENSAGEM_ERRO**", $this->erro->mensagens[0]);
		} else {
			$this->configura_subtemplate($this->subtemplate);
		}
		$this->finaliza_pagina();
	}

	function retorna_saldo()
	{
		$this->entidade = new Entidade($this->sessao->get("CodEnt"));
		$this->associado = new Associado($this->sessao->get("CodAss"), $this->entidade);
		$this->fonte_pg = new FontePg($this->associado->codfpg);
		$saldo = new Saldo($this->associado);
		return $saldo->limite_mes($this->fonte_pg->mes_universal_atual);
	}

	function calcula_mes_atual($diacor)
	{
		$dia = date("d", time());
		$mes = date("m", time());

		if ($dia <= $diacor) {
			$mesatu = $mes * 1;
		} else {
			$mesatu = $mes + 1;
		}

		if ($mesatu == 13) {
			$mesatu = 1;
		}
		$this->sessao->set("MesAtu", $mesatu);
	}

	function exibe_mensagem()
	{

		if ($this->sessao->get('CodEnt')) {
			$this->bd->consulta($this->sql->mensagem_ass($this->sessao->get('CodEnt')));
			$msg = $this->bd->proximo_registro();
			$msm = $msg['entmsgass'];
		}
		$this->template->associa("**MSG**", $msm);
	}

	function verifica_periodo_bloqueio_entidade()
	{
		$restricao = new RestricaoEntidade($this->cartao->associado->entidade, $this->cartao->associado->fontepg->codigo);
		$mensagem = null;

		if ($restricao->bloqueado_periodo) {
			$mensagem = "<font color=\"red\" size=\"2\"><b><u>ATEN��O: A ENTIDADE ENCONTRA-SE EM PER�ODO DE BLOQUEIO</u></b></font><br>" . "<font size=\"2\" color=\"black\">";
			$mensagem .= "<br>Em caso de d�vidas, entre em contato com sua ENTIDADE.";
		}
		$this->template->associa("**OBS**", $mensagem);
	}

	private function verificaNotificacoesAut()
	{
		$this->bd->consulta($this->sql->notificacoes_autorizacao_associado($this->associado->entidade->codigo, $this->associado->codigo));
		if ($this->bd->num_linhas() > 0) {
			header('Location: ./tecbiz.php?a=9c0d3b');
		}
	}
}

if (base64_decode($_REQUEST['modo']) === 'senhaEmail') {
	$dados = base64_decode($_REQUEST['dados']);
	$dados = explode("^", $dados);

	if ($dados[1] != date('Y-m-d')) {
		die("Link expirado!");
	}
	$cartao = explode(' ', $dados[0]);
	$_REQUEST['cartao1'] = $cartao[0];
	$_REQUEST['cartao2'] = $cartao[1];
	$_REQUEST['cartao3'] = $cartao[2];
	$_REQUEST['cartao4'] = $cartao[3];
	$_REQUEST['senha'] = _SENHA_PADRAO_;
}

if (base64_decode($_REQUEST['modo']) === 'confirmaEmail') {
	$dados = base64_decode($_REQUEST['dados']);
	$dados = explode("^", $dados);

	if ($dados[1] != date('Y-m-d')) {
		die("Link expirado!");
	}

	$complemento = null;
	$fechar      = null;
	$data 		 = null;

	if (@$_REQUEST['origem'] == 'APP') {
		$complemento = ' no aplicativo';
		$fechar      = '1';
		$data = null;
		// Para aplicativo, redirecionar via deep link
		$sql = new SQL();
		$bd = new BD();

		$dadosBruto = base64_decode($_REQUEST['dados']);
		$dadosSplit = explode("^", $dadosBruto);

		$numeroCartao = preg_replace('/\D+/', '', $dadosSplit[0]);
		$Cartao = new Cartao($numeroCartao);
		$bd->consulta($sql->marca_email_ass_confirmado($Cartao->associado->entidade->codigo, $Cartao->associado->codigo, $data));

		// Construir URL do deep link para o app
		$deepLinkParams = [
			'modo' => $_REQUEST['modo'],
			'dados' => $_REQUEST['dados'],
			'origem' => $_REQUEST['origem'],
			'status' => 'success',
			'message' => 'E-mail validado com sucesso!',
			// ADICIONAR DADOS DO USU�RIO PARA O APP
			'codass' => $Cartao->associado->codigo,
			'codent' => $Cartao->associado->entidade->codigo,
			'cartao' => $numeroCartao,
			'emailCadastro' => $Cartao->associado->asslogema,
			'portador' => $Cartao->associado->assnomass
		];

		$appUrl = "tecbizapp://email_confirmation?" . http_build_query($deepLinkParams);

		// Log para debug
		error_log("Deep Link gerado: " . $appUrl);

		// HTML para redirecionamento inteligente para o app
?>
		<!DOCTYPE html>
		<html>

		<head>
			<meta charset="UTF-8">
			<title>Redirecionando para o App TecBiz...</title>
			<meta name="viewport" content="width=device-width, initial-scale=1.0">
			<style>
				body {
					font-family: Arial, sans-serif;
					text-align: center;
					padding: 50px 20px;
					background: #F7F7F7;
					color: black;
					margin: 0;
				}

				.container {
					max-width: 400px;
					margin: 0 auto;
					background: rgba(255, 255, 255, 0.1);
					padding: 30px;
					border-radius: 15px;
					backdrop-filter: blur(10px);
				}

				.logo {
					font-size: 2.5em;
					font-weight: bold;
					margin-bottom: 20px;
				}

				.message {
					font-size: 1.1em;
					margin-bottom: 30px;
					line-height: 1.5;
				}

				.button {
					display: inline-block;
					background: #FFB805;
					color: white;
					padding: 12px 24px;
					text-decoration: none;
					border-radius: 8px;
					margin: 10px;
					font-weight: bold;
					transition: background 0.3s;
				}

				.button:hover {
					background: #45a049;
				}

				.button.secondary {
					background: #2196F3;
				}

				.button.secondary:hover {
					background: #1976D2;
				}

				.spinner {
					border: 3px solid rgba(255, 255, 255, 0.3);
					border-top: 3px solid #DCEDDC;
					border-radius: 50%;
					width: 30px;
					height: 30px;
					animation: spin 1s linear infinite;
					margin: 20px auto;
				}

				@keyframes spin {
					0% {
						transform: rotate(0deg);
					}

					100% {
						transform: rotate(360deg);
					}
				}
			</style>
		</head>

		<body>
			<div class="container">
				<div class="logo">TecBiz</div>
				<div class="message">
					<div class="spinner"></div>
					E-mail validado com sucesso!<br>
					Redirecionando para o aplicativo...
				</div>
				<div id="manual-options" style="display:none;">
					<p>Se o redirecionamento não funcionou:</p>
					<button onclick="tryOpenApp()" class="button">
						🔄 Tentar Abrir App Novamente
					</button>
					<a href="<?php echo htmlspecialchars($appUrl); ?>" class="button" onclick="console.log('🔗 Clique manual - Custom Scheme');">
						📱 Abrir no App
					</a>
					<a href="./tecbiz.php?a=842228&endereco=<?php echo urlencode($Cartao->associado->asslogema); ?>" class="button secondary">
						🌐 Abrir no Navegador
					</a>
				</div>
			</div>

			<script>
				// Tentar redirecionar para o app
				function redirectToApp() {
					const appUrl = <?php echo json_encode($appUrl); ?>;
					const fallbackUrl = "./tecbiz.php?a=842228&endereco=<?php echo urlencode($Cartao->associado->asslogema); ?>";

					console.log('🔗 Tentando redirecionamento para app...');
					console.log('📱 App URL:', appUrl);

					// Detectar se é iOS
					const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
					const isAndroid = /Android/.test(navigator.userAgent);
					const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);

					console.log('📱 Plataforma detectada:', isIOS ? 'iOS' : isAndroid ? 'Android' : 'Desconhecida');
					console.log('🌐 Safari detectado:', isSafari);

					if (isIOS) {
						// Para iOS: estratégia mais robusta
						console.log('🍎 iOS detectado - usando estratégia específica...');

						// Criar iframe invisível para tentar abrir o app (funciona melhor no iOS)
						const iframe = document.createElement('iframe');
						iframe.style.display = 'none';
						iframe.src = appUrl;
						document.body.appendChild(iframe);

						// Remover iframe após tentativa
						setTimeout(function() {
							document.body.removeChild(iframe);
						}, 1000);

						// Fallback: tentar window.location após 2 segundos
						setTimeout(function() {
							console.log('🔄 Tentando window.location como fallback...');
							try {
								window.location.href = appUrl;
							} catch (e) {
								console.log('❌ Erro ao tentar window.location:', e);
							}
						}, 2000);

					} else {
						// Para Android: usar custom scheme diretamente
						console.log('🤖 Android detectado - usando custom scheme...');
						window.location.href = appUrl;
					}

					// Mostrar opções manuais após 4 segundos
					setTimeout(function() {
						console.log('⏰ Mostrando opções manuais...');
						document.getElementById('manual-options').style.display = 'block';
					}, 4000);

					// Fallback para navegador após 8 segundos se o app não abrir
					setTimeout(function() {
						// Verificar se ainda estamos na página (app não abriu)
						if (!document.hidden) {
							console.log('🌐 App não detectado, redirecionando para navegador...');
							window.location.href = fallbackUrl;
						}
					}, 8000);
				}

				// Função para tentar abrir app manualmente
				function tryOpenApp() {
					const appUrl = <?php echo json_encode($appUrl); ?>;
					console.log('🔗 Tentativa manual de abrir app:', appUrl);

					// Tentar múltiplas abordagens
					try {
						// Método 1: window.location
						window.location.href = appUrl;
					} catch (e1) {
						console.log('❌ Método 1 falhou:', e1);

						try {
							// Método 2: window.open
							window.open(appUrl, '_self');
						} catch (e2) {
							console.log('❌ Método 2 falhou:', e2);

							try {
								// Método 3: criar link e clicar
								const link = document.createElement('a');
								link.href = appUrl;
								link.click();
							} catch (e3) {
								console.log('❌ Método 3 falhou:', e3);
							}
						}
					}
				}

				// Executar redirecionamento quando a página carregar
				window.addEventListener('load', function() {
					console.log('📄 Página carregada, iniciando redirecionamento...');
					redirectToApp();
				});

				// Detectar quando o usuário volta para a página (app não abriu)
				document.addEventListener('visibilitychange', function() {
					if (!document.hidden) {
						console.log('👁️ Usuário voltou para a página');
						// Usuário voltou para a página, mostrar opções
						setTimeout(function() {
							document.getElementById('manual-options').style.display = 'block';
						}, 1000);
					}
				});

				// Detectar se a página perdeu foco (possível abertura do app)
				window.addEventListener('blur', function() {
					console.log('🔄 Página perdeu foco - app pode ter aberto');
				});

				// Detectar se a página ganhou foco novamente
				window.addEventListener('focus', function() {
					console.log('👁️ Página ganhou foco novamente');
				});
			</script>
		</body>

		</html>
<?php
		die();
	} else {
		// Para navegador web, manter comportamento original
		$sql = new SQL();
		$bd = new BD();
		$Cartao = new Cartao($dados[0]);

		$bd->consulta($sql->marca_email_ass_confirmado($Cartao->associado->entidade->codigo, $Cartao->associado->codigo, $data));

		echo ("<script>
			alert('**ATEN��O**\\n\\nE-mail validado com sucesso!\\nInforme seus dados de acesso{$complemento}!');
			window.open('./tecbiz.php?a=842228&fechar={$fechar}&endereco={$Cartao->associado->asslogema}', '_self');
			</script>");
		die();
	}
}
new PGAssLogin($_REQUEST);
