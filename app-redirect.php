<?php
/**
 * Arquivo de redirecionamento para Universal Links (iOS)
 * Este arquivo serve como ponte entre Universal Links e Custom URL Schemes
 */

// Obter todos os parâmetros da URL
$params = $_GET;

// Log para debug
error_log("Universal Link acessado com parâmetros: " . json_encode($params));

// Construir URL do custom scheme
$appUrl = "tecbizapp://email_confirmation?" . http_build_query($params);

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecionando para o App TecBiz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #FFB805 0%, #FF8C00 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #FFB805;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .button {
            display: inline-block;
            background: #FFB805;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            margin: 5px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #FF8C00;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #5a6268;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #FFB805;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        #manual-options {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">TB</div>
        <h1>TecBiz Associado</h1>
        <p>Redirecionando para o aplicativo...</p>
        <div class="spinner"></div>
        
        <div id="manual-options" style="display:none;">
            <p>Se o redirecionamento não funcionou:</p>
            <a href="<?php echo htmlspecialchars($appUrl); ?>" class="button">
                Abrir no App
            </a>
            <a href="./pg_ass_login.php?<?php echo http_build_query($params); ?>" class="button secondary">
                Abrir no Navegador
            </a>
        </div>
    </div>

    <script>
        console.log('🔗 Universal Link - Redirecionamento iniciado');
        console.log('📱 App URL:', <?php echo json_encode($appUrl); ?>);
        
        function redirectToApp() {
            const appUrl = <?php echo json_encode($appUrl); ?>;
            
            console.log('🍎 Tentando abrir app via Universal Link...');
            
            // Tentar abrir o app
            window.location.href = appUrl;
            
            // Mostrar opções manuais após 3 segundos
            setTimeout(function() {
                console.log('⏰ Mostrando opções manuais...');
                document.getElementById('manual-options').style.display = 'block';
            }, 3000);
        }
        
        // Executar redirecionamento quando a página carregar
        window.addEventListener('load', function() {
            console.log('📄 Página Universal Link carregada');
            redirectToApp();
        });
        
        // Detectar quando o usuário volta para a página
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('👁️ Usuário voltou para Universal Link');
                setTimeout(function() {
                    document.getElementById('manual-options').style.display = 'block';
                }, 1000);
            }
        });
    </script>
</body>
</html>
