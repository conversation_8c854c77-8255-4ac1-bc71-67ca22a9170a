{"name": "tecbizexpoapp", "version": "5.0.9", "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/metro-runtime": "~6.1.1", "expo": "^54.0.0", "expo-asset": "^12.0.8", "expo-dev-client": "~6.0.12", "expo-device": "~8.0.7", "expo-local-authentication": "~17.0.7", "expo-notifications": "~0.32.11", "expo-screen-orientation": "~9.0.7", "expo-secure-store": "~15.0.7", "expo-status-bar": "~3.0.8", "firebase": "^12.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-web": "^0.21.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.1.10", "babel-preset-expo": "^54.0.0", "typescript": "~5.9.2"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}